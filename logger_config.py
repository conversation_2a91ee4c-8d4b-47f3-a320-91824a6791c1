"""
日志配置模块
负责设置和管理日志系统
"""

import logging
import logging.handlers
import os
import sys
from typing import Optional
from config_manager import LoggingConfig


class ColoredFormatter(logging.Formatter):
    """彩色日志格式化器"""
    
    # 颜色代码
    COLORS = {
        'DEBUG': '\033[36m',    # 青色
        'INFO': '\033[32m',     # 绿色
        'WARNING': '\033[33m',  # 黄色
        'ERROR': '\033[31m',    # 红色
        'CRITICAL': '\033[35m', # 紫色
        'RESET': '\033[0m'      # 重置
    }
    
    def format(self, record):
        # 添加颜色
        if record.levelname in self.COLORS:
            record.levelname = f"{self.COLORS[record.levelname]}{record.levelname}{self.COLORS['RESET']}"
        
        return super().format(record)


class LoggerManager:
    """日志管理器"""
    
    def __init__(self):
        self.logger = None
        self.file_handler = None
        self.console_handler = None
    
    def setup_logging(self, config: LoggingConfig) -> None:
        """设置日志系统"""
        # 创建根日志器
        self.logger = logging.getLogger()
        self.logger.setLevel(getattr(logging, config.level.upper(), logging.INFO))
        
        # 清除现有处理器
        self.logger.handlers.clear()
        
        # 设置日志格式
        log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        date_format = '%Y-%m-%d %H:%M:%S'
        
        # 设置控制台处理器
        self._setup_console_handler(log_format, date_format)
        
        # 设置文件处理器
        self._setup_file_handler(config, log_format, date_format)
        
        # 设置第三方库日志级别
        self._configure_third_party_loggers()
        
        logging.info("日志系统初始化完成")
    
    def _setup_console_handler(self, log_format: str, date_format: str) -> None:
        """设置控制台日志处理器"""
        self.console_handler = logging.StreamHandler(sys.stdout)
        
        # 使用彩色格式化器
        if sys.stdout.isatty():  # 如果是终端，使用彩色输出
            formatter = ColoredFormatter(log_format, date_format)
        else:
            formatter = logging.Formatter(log_format, date_format)
        
        self.console_handler.setFormatter(formatter)
        self.logger.addHandler(self.console_handler)
    
    def _setup_file_handler(self, config: LoggingConfig, log_format: str, date_format: str) -> None:
        """设置文件日志处理器"""
        try:
            # 确保日志目录存在
            log_dir = os.path.dirname(config.file) if os.path.dirname(config.file) else '.'
            os.makedirs(log_dir, exist_ok=True)
            
            # 创建轮转文件处理器
            max_bytes = config.max_size_mb * 1024 * 1024  # 转换为字节
            self.file_handler = logging.handlers.RotatingFileHandler(
                config.file,
                maxBytes=max_bytes,
                backupCount=config.backup_count,
                encoding='utf-8'
            )
            
            formatter = logging.Formatter(log_format, date_format)
            self.file_handler.setFormatter(formatter)
            self.logger.addHandler(self.file_handler)
            
        except Exception as e:
            logging.error(f"设置文件日志处理器失败: {e}")
    
    def _configure_third_party_loggers(self) -> None:
        """配置第三方库的日志级别"""
        # 设置邮件相关库的日志级别
        logging.getLogger('imaplib').setLevel(logging.WARNING)
        logging.getLogger('smtplib').setLevel(logging.WARNING)
        
        # 设置其他可能产生大量日志的库
        logging.getLogger('urllib3').setLevel(logging.WARNING)
        logging.getLogger('requests').setLevel(logging.WARNING)
    
    def get_logger(self, name: str) -> logging.Logger:
        """获取指定名称的日志器"""
        return logging.getLogger(name)
    
    def set_level(self, level: str) -> None:
        """动态设置日志级别"""
        if self.logger:
            self.logger.setLevel(getattr(logging, level.upper(), logging.INFO))
            logging.info(f"日志级别已设置为: {level}")
    
    def close(self) -> None:
        """关闭日志处理器"""
        if self.file_handler:
            self.file_handler.close()
        if self.console_handler:
            self.console_handler.close()


class ExceptionHandler:
    """异常处理器"""
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        self.logger = logger or logging.getLogger(__name__)
    
    def handle_exception(self, exc_type, exc_value, exc_traceback):
        """全局异常处理器"""
        if issubclass(exc_type, KeyboardInterrupt):
            # 不记录键盘中断异常
            sys.__excepthook__(exc_type, exc_value, exc_traceback)
            return
        
        self.logger.critical(
            "未捕获的异常",
            exc_info=(exc_type, exc_value, exc_traceback)
        )
    
    def log_and_reraise(self, message: str = ""):
        """记录异常并重新抛出"""
        def decorator(func):
            def wrapper(*args, **kwargs):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    error_msg = f"{message}: {e}" if message else str(e)
                    self.logger.error(error_msg, exc_info=True)
                    raise
            return wrapper
        return decorator
    
    def log_and_continue(self, message: str = "", default_return=None):
        """记录异常但继续执行"""
        def decorator(func):
            def wrapper(*args, **kwargs):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    error_msg = f"{message}: {e}" if message else str(e)
                    self.logger.error(error_msg, exc_info=True)
                    return default_return
            return wrapper
        return decorator


# 全局日志管理器实例
logger_manager = LoggerManager()

# 全局异常处理器实例
exception_handler = ExceptionHandler()


def setup_global_logging(config: LoggingConfig) -> None:
    """设置全局日志系统"""
    logger_manager.setup_logging(config)
    
    # 设置全局异常处理器
    sys.excepthook = exception_handler.handle_exception


def get_logger(name: str) -> logging.Logger:
    """获取日志器的便捷函数"""
    return logger_manager.get_logger(name)
