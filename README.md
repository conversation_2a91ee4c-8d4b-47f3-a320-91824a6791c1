# 163邮箱转发服务

一个用于自动转发163邮箱邮件的Python服务，支持实时监控、智能过滤和多目标转发。

## 功能特性

- 🔄 **自动监控**: 实时监控163邮箱新邮件
- 📧 **多目标转发**: 支持同时转发到多个邮箱地址
- 🎯 **智能过滤**: 灵活的转发规则配置（关键词、白名单、黑名单）
- 📝 **完整日志**: 详细的日志记录和轮转管理
- 🛡️ **错误处理**: 完善的异常处理和重试机制
- ⚙️ **易于配置**: YAML配置文件，简单直观
- 🧪 **测试支持**: 内置测试功能，验证配置正确性

## 快速开始

### 1. 环境准备

```bash
# 创建虚拟环境
uv venv -p 3.11

# 激活虚拟环境
source .venv/bin/activate

# 安装依赖（如果网络有问题，可以配置代理）
# export http_proxy="http://127.0.0.1:7890"
# export https_proxy="http://127.0.0.1:7890"
uv pip install -r requirements.txt
```

### 2. 配置邮箱

#### 2.1 获取163邮箱SMTP授权码

1. 登录163邮箱网页版
2. 进入"设置" → "POP3/SMTP/IMAP"
3. 开启"IMAP/SMTP服务"
4. 获取"授权密码"（这就是SMTP授权码）

#### 2.2 编辑配置文件

编辑 `config.yaml` 文件：

```yaml
# 163邮箱配置
email_163:
  username: "<EMAIL>"  # 替换为您的163邮箱
  password: "XKSatD6M4xL2EMiV"    # 替换为您的SMTP授权码

# 转发配置
forward:
  target_emails:
    - "<EMAIL>"  # 替换为实际的转发目标邮箱
```

### 3. 测试配置

```bash
# 发送测试邮件
python main.py --test

# 查看服务状态
python main.py --status
```

### 4. 运行服务

```bash
# 前台运行
python main.py

# 后台运行（推荐用于生产环境）
nohup python main.py > service.log 2>&1 &
```

## 详细配置说明

### 转发规则配置

```yaml
forward:
  rules:
    forward_all: true  # 转发所有邮件
    keywords: ["重要", "urgent"]  # 关键词过滤
    sender_whitelist: ["<EMAIL>"]  # 发件人白名单
    sender_blacklist: ["<EMAIL>"]  # 发件人黑名单
```

### 服务配置

```yaml
service:
  check_interval: 60  # 检查间隔（秒）
  max_emails_per_check: 10  # 每次最多处理邮件数
  mark_as_read: true  # 是否标记已读
```

### 日志配置

```yaml
logging:
  level: "INFO"  # 日志级别
  file: "email_forwarder.log"  # 日志文件
  max_size_mb: 10  # 日志文件最大大小
  backup_count: 5  # 备份文件数量
```

## 命令行选项

```bash
# 基本用法
python main.py [选项]

# 选项说明
--config, -c    指定配置文件路径（默认: config.yaml）
--test, -t      发送测试邮件并退出
--status, -s    显示服务状态
--daemon, -d    以守护进程模式运行
--log-level     设置日志级别（DEBUG/INFO/WARNING/ERROR）

# 示例
python main.py --config my_config.yaml --log-level DEBUG
python main.py --test
python main.py --status
```

## 运行测试

```bash
# 运行单元测试和集成测试
python test_service.py
```

## 故障排除

### 常见问题

1. **SMTP认证失败**
   - 确认163邮箱已开启SMTP服务
   - 确认使用的是SMTP授权码，不是登录密码
   - 检查邮箱地址是否正确

2. **网络连接问题**
   - 检查防火墙设置
   - 确认可以访问163邮箱服务器
   - 如果在企业网络，可能需要配置代理

3. **邮件不转发**
   - 检查转发规则配置
   - 查看日志文件了解详细错误信息
   - 确认目标邮箱地址正确

### 日志分析

日志文件位置：`email_forwarder.log`

```bash
# 查看实时日志
tail -f email_forwarder.log

# 搜索错误信息
grep ERROR email_forwarder.log
```

## 安全注意事项

- 🔐 **保护配置文件**: 配置文件包含敏感信息，请妥善保管
- 🚫 **不要提交密码**: 不要将包含真实密码的配置文件提交到版本控制
- 🔄 **定期更换密码**: 建议定期更换SMTP授权码
- 🛡️ **网络安全**: 在生产环境中使用时，确保网络连接安全

## 许可证

本项目采用 MIT 许可证。
