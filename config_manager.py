"""
配置管理模块
负责读取和验证配置文件
"""

import yaml
import os
from typing import Dict, List, Any
from dataclasses import dataclass


@dataclass
class EmailConfig:
    """163邮箱配置"""
    username: str
    password: str
    imap_server: str
    imap_port: int
    smtp_server: str
    smtp_port: int


@dataclass
class ForwardRules:
    """转发规则配置"""
    forward_all: bool
    keywords: List[str]
    sender_whitelist: List[str]
    sender_blacklist: List[str]


@dataclass
class ForwardConfig:
    """转发配置"""
    target_emails: List[str]
    rules: ForwardRules


@dataclass
class ServiceConfig:
    """服务配置"""
    check_interval: int
    max_emails_per_check: int
    mark_as_read: bool


@dataclass
class LoggingConfig:
    """日志配置"""
    level: str
    file: str
    max_size_mb: int
    backup_count: int


@dataclass
class AppConfig:
    """应用总配置"""
    email_163: EmailConfig
    forward: ForwardConfig
    service: ServiceConfig
    logging: LoggingConfig


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: str = "config.yaml"):
        self.config_file = config_file
        self._config: AppConfig = None
    
    def load_config(self) -> AppConfig:
        """加载配置文件"""
        if not os.path.exists(self.config_file):
            raise FileNotFoundError(f"配置文件不存在: {self.config_file}")
        
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)
            
            # 验证配置
            self._validate_config(config_data)
            
            # 构建配置对象
            self._config = self._build_config(config_data)
            return self._config
            
        except yaml.YAMLError as e:
            raise ValueError(f"配置文件格式错误: {e}")
        except Exception as e:
            raise ValueError(f"加载配置失败: {e}")
    
    def _validate_config(self, config_data: Dict[str, Any]) -> None:
        """验证配置数据"""
        required_sections = ['email_163', 'forward', 'service', 'logging']
        
        for section in required_sections:
            if section not in config_data:
                raise ValueError(f"缺少必需的配置节: {section}")
        
        # 验证163邮箱配置
        email_config = config_data['email_163']
        required_email_fields = ['username', 'password', 'imap_server', 'imap_port', 'smtp_server', 'smtp_port']
        for field in required_email_fields:
            if field not in email_config:
                raise ValueError(f"缺少163邮箱配置字段: {field}")
        
        # 验证转发配置
        forward_config = config_data['forward']
        if 'target_emails' not in forward_config or not forward_config['target_emails']:
            raise ValueError("必须配置至少一个转发目标邮箱")
        
        # 验证邮箱地址格式
        import re
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        
        # 验证163邮箱地址
        if not re.match(email_pattern, email_config['username']):
            raise ValueError(f"163邮箱地址格式不正确: {email_config['username']}")
        
        # 验证转发目标邮箱地址
        for email in forward_config['target_emails']:
            if not re.match(email_pattern, email):
                raise ValueError(f"转发目标邮箱地址格式不正确: {email}")
    
    def _build_config(self, config_data: Dict[str, Any]) -> AppConfig:
        """构建配置对象"""
        # 构建163邮箱配置
        email_data = config_data['email_163']
        email_config = EmailConfig(
            username=email_data['username'],
            password=email_data['password'],
            imap_server=email_data['imap_server'],
            imap_port=email_data['imap_port'],
            smtp_server=email_data['smtp_server'],
            smtp_port=email_data['smtp_port']
        )
        
        # 构建转发规则
        rules_data = config_data['forward'].get('rules', {})
        forward_rules = ForwardRules(
            forward_all=rules_data.get('forward_all', True),
            keywords=rules_data.get('keywords', []),
            sender_whitelist=rules_data.get('sender_whitelist', []),
            sender_blacklist=rules_data.get('sender_blacklist', [])
        )
        
        # 构建转发配置
        forward_data = config_data['forward']
        forward_config = ForwardConfig(
            target_emails=forward_data['target_emails'],
            rules=forward_rules
        )
        
        # 构建服务配置
        service_data = config_data['service']
        service_config = ServiceConfig(
            check_interval=service_data.get('check_interval', 60),
            max_emails_per_check=service_data.get('max_emails_per_check', 10),
            mark_as_read=service_data.get('mark_as_read', True)
        )
        
        # 构建日志配置
        logging_data = config_data['logging']
        logging_config = LoggingConfig(
            level=logging_data.get('level', 'INFO'),
            file=logging_data.get('file', 'email_forwarder.log'),
            max_size_mb=logging_data.get('max_size_mb', 10),
            backup_count=logging_data.get('backup_count', 5)
        )
        
        return AppConfig(
            email_163=email_config,
            forward=forward_config,
            service=service_config,
            logging=logging_config
        )
    
    @property
    def config(self) -> AppConfig:
        """获取当前配置"""
        if self._config is None:
            self.load_config()
        return self._config
    
    def reload_config(self) -> AppConfig:
        """重新加载配置"""
        self._config = None
        return self.load_config()


# 全局配置管理器实例
config_manager = ConfigManager()
