"""
邮件转发模块
负责通过SMTP发送转发邮件
"""

import smtplib
import logging
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.mime.base import MIMEBase
from email import encoders
from email.utils import formataddr
from typing import List, Optional
from config_manager import EmailConfig
from email_receiver import EmailInfo


class EmailForwarder:
    """邮件转发器"""
    
    def __init__(self, email_config: EmailConfig):
        self.config = email_config
        self.smtp_conn: Optional[smtplib.SMTP_SSL] = None
        self.logger = logging.getLogger(__name__)
    
    def connect(self) -> bool:
        """连接到SMTP服务器"""
        try:
            self.logger.info(f"连接到SMTP服务器: {self.config.smtp_server}:{self.config.smtp_port}")
            self.smtp_conn = smtplib.SMTP_SSL(self.config.smtp_server, self.config.smtp_port)
            self.smtp_conn.login(self.config.username, self.config.password)
            self.logger.info("SMTP连接成功")
            return True
        except Exception as e:
            self.logger.error(f"SMTP连接失败: {e}")
            return False
    
    def disconnect(self) -> None:
        """断开SMTP连接"""
        if self.smtp_conn:
            try:
                self.smtp_conn.quit()
                self.logger.info("SMTP连接已断开")
            except Exception as e:
                self.logger.error(f"断开SMTP连接时出错: {e}")
            finally:
                self.smtp_conn = None
    
    def forward_email(self, email_info: EmailInfo, target_emails: List[str]) -> bool:
        """转发邮件到目标地址"""
        if not self.smtp_conn:
            if not self.connect():
                return False
        
        try:
            # 为每个目标地址发送邮件
            success_count = 0
            for target_email in target_emails:
                if self._send_forwarded_email(email_info, target_email):
                    success_count += 1
                    self.logger.info(f"邮件成功转发到: {target_email}")
                else:
                    self.logger.error(f"邮件转发失败: {target_email}")
            
            return success_count > 0
            
        except Exception as e:
            self.logger.error(f"转发邮件时出错: {e}")
            return False
    
    def _send_forwarded_email(self, email_info: EmailInfo, target_email: str) -> bool:
        """发送转发邮件到单个目标地址"""
        try:
            # 创建邮件消息
            msg = MIMEMultipart('alternative')
            
            # 设置邮件头
            msg['From'] = formataddr((f"转发自 {self.config.username}", self.config.username))
            msg['To'] = target_email
            msg['Subject'] = f"[转发] {email_info.subject}"
            
            # 创建邮件正文
            body_text, body_html = self._create_forwarded_body(email_info)
            
            # 添加纯文本部分
            if body_text:
                text_part = MIMEText(body_text, 'plain', 'utf-8')
                msg.attach(text_part)
            
            # 添加HTML部分
            if body_html:
                html_part = MIMEText(body_html, 'html', 'utf-8')
                msg.attach(html_part)
            
            # 如果没有正文内容，添加默认文本
            if not body_text and not body_html:
                default_text = f"转发邮件\n\n原发件人: {email_info.sender}\n原主题: {email_info.subject}\n发送时间: {email_info.date}"
                text_part = MIMEText(default_text, 'plain', 'utf-8')
                msg.attach(text_part)
            
            # 添加附件
            for filename, content in email_info.attachments:
                self._add_attachment(msg, filename, content)
            
            # 发送邮件
            self.smtp_conn.send_message(msg)
            return True
            
        except Exception as e:
            self.logger.error(f"发送邮件到 {target_email} 失败: {e}")
            return False
    
    def _create_forwarded_body(self, email_info: EmailInfo) -> tuple[str, str]:
        """创建转发邮件的正文"""
        # 转发信息头
        forward_header = f"""
========== 转发邮件 ==========
原发件人: {email_info.sender}
原主题: {email_info.subject}
发送时间: {email_info.date}
原收件人: {', '.join(email_info.recipients)}
================================

"""
        
        # 纯文本正文
        body_text = forward_header
        if email_info.content_text:
            body_text += email_info.content_text
        
        # HTML正文
        body_html = ""
        if email_info.content_html:
            html_header = f"""
<div style="border: 1px solid #ccc; padding: 10px; margin: 10px 0; background-color: #f9f9f9;">
    <h3>转发邮件</h3>
    <p><strong>原发件人:</strong> {self._html_escape(email_info.sender)}</p>
    <p><strong>原主题:</strong> {self._html_escape(email_info.subject)}</p>
    <p><strong>发送时间:</strong> {self._html_escape(email_info.date)}</p>
    <p><strong>原收件人:</strong> {self._html_escape(', '.join(email_info.recipients))}</p>
</div>
<hr>
"""
            body_html = html_header + email_info.content_html
        
        return body_text, body_html
    
    def _html_escape(self, text: str) -> str:
        """HTML转义"""
        return (text.replace('&', '&amp;')
                   .replace('<', '&lt;')
                   .replace('>', '&gt;')
                   .replace('"', '&quot;')
                   .replace("'", '&#x27;'))
    
    def _add_attachment(self, msg: MIMEMultipart, filename: str, content: bytes) -> None:
        """添加附件到邮件"""
        try:
            part = MIMEBase('application', 'octet-stream')
            part.set_payload(content)
            encoders.encode_base64(part)
            part.add_header(
                'Content-Disposition',
                f'attachment; filename= {filename}'
            )
            msg.attach(part)
            self.logger.debug(f"添加附件: {filename}")
        except Exception as e:
            self.logger.error(f"添加附件失败 {filename}: {e}")
    
    def send_test_email(self, target_email: str) -> bool:
        """发送测试邮件"""
        if not self.smtp_conn:
            if not self.connect():
                return False
        
        try:
            msg = MIMEMultipart()
            msg['From'] = formataddr((f"163邮箱转发服务", self.config.username))
            msg['To'] = target_email
            msg['Subject'] = "163邮箱转发服务测试邮件"
            
            body = """
这是一封测试邮件，用于验证163邮箱转发服务的配置是否正确。

如果您收到这封邮件，说明：
1. SMTP服务器连接正常
2. 邮箱认证成功
3. 邮件发送功能正常

服务配置信息：
- 发送邮箱: {sender}
- 发送时间: {time}

请确认转发服务已正常工作。
""".format(
                sender=self.config.username,
                time=self._get_current_time()
            )
            
            text_part = MIMEText(body, 'plain', 'utf-8')
            msg.attach(text_part)
            
            self.smtp_conn.send_message(msg)
            self.logger.info(f"测试邮件发送成功: {target_email}")
            return True
            
        except Exception as e:
            self.logger.error(f"发送测试邮件失败: {e}")
            return False
    
    def _get_current_time(self) -> str:
        """获取当前时间字符串"""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")
