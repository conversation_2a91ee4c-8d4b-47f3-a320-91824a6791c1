#!/bin/bash

# 163邮箱转发服务停止脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 停止服务
stop_service() {
    print_info "=== 停止163邮箱转发服务 ==="
    
    # 检查PID文件
    if [ -f "service.pid" ]; then
        local pid=$(cat service.pid)
        print_info "发现服务PID: $pid"
        
        # 检查进程是否存在
        if kill -0 "$pid" 2>/dev/null; then
            print_info "正在停止服务..."
            
            # 发送SIGTERM信号
            kill "$pid"
            
            # 等待进程结束
            local count=0
            while kill -0 "$pid" 2>/dev/null && [ $count -lt 30 ]; do
                sleep 1
                count=$((count + 1))
                echo -n "."
            done
            echo
            
            # 检查进程是否已结束
            if kill -0 "$pid" 2>/dev/null; then
                print_warning "进程未正常结束，强制终止..."
                kill -9 "$pid"
                sleep 2
                
                if kill -0 "$pid" 2>/dev/null; then
                    print_error "无法停止进程 $pid"
                    exit 1
                else
                    print_success "进程已强制终止"
                fi
            else
                print_success "服务已正常停止"
            fi
        else
            print_warning "PID文件中的进程不存在"
        fi
        
        # 删除PID文件
        rm -f service.pid
        print_info "已删除PID文件"
    else
        print_warning "未找到PID文件，尝试查找运行中的服务进程..."
        
        # 查找可能的服务进程
        local pids=$(pgrep -f "python.*main.py" || true)
        
        if [ -n "$pids" ]; then
            print_info "发现可能的服务进程: $pids"
            
            for pid in $pids; do
                print_info "停止进程 $pid..."
                kill "$pid"
            done
            
            sleep 3
            
            # 检查是否还有残留进程
            local remaining=$(pgrep -f "python.*main.py" || true)
            if [ -n "$remaining" ]; then
                print_warning "强制终止残留进程: $remaining"
                pkill -9 -f "python.*main.py" || true
            fi
            
            print_success "所有相关进程已停止"
        else
            print_info "未发现运行中的服务进程"
        fi
    fi
}

# 显示服务状态
show_status() {
    print_info "=== 服务状态 ==="
    
    # 检查PID文件
    if [ -f "service.pid" ]; then
        local pid=$(cat service.pid)
        if kill -0 "$pid" 2>/dev/null; then
            print_success "服务正在运行 (PID: $pid)"
            
            # 显示进程信息
            echo "进程信息:"
            ps -p "$pid" -o pid,ppid,cmd,etime,pcpu,pmem 2>/dev/null || true
        else
            print_warning "PID文件存在但进程不存在"
            rm -f service.pid
        fi
    else
        # 查找可能的服务进程
        local pids=$(pgrep -f "python.*main.py" || true)
        
        if [ -n "$pids" ]; then
            print_warning "发现可能的服务进程（无PID文件）:"
            for pid in $pids; do
                ps -p "$pid" -o pid,ppid,cmd,etime,pcpu,pmem 2>/dev/null || true
            done
        else
            print_info "服务未运行"
        fi
    fi
    
    # 显示日志文件信息
    if [ -f "service.log" ]; then
        local log_size=$(du -h service.log | cut -f1)
        local log_lines=$(wc -l < service.log)
        print_info "日志文件: service.log (大小: $log_size, 行数: $log_lines)"
    fi
    
    if [ -f "email_forwarder.log" ]; then
        local app_log_size=$(du -h email_forwarder.log | cut -f1)
        local app_log_lines=$(wc -l < email_forwarder.log)
        print_info "应用日志: email_forwarder.log (大小: $app_log_size, 行数: $app_log_lines)"
    fi
}

# 显示帮助信息
show_help() {
    echo "163邮箱转发服务停止脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --help, -h     显示帮助信息"
    echo "  --status, -s   显示服务状态"
    echo "  --force, -f    强制停止所有相关进程"
    echo ""
    echo "示例:"
    echo "  $0             # 正常停止服务"
    echo "  $0 --status    # 显示服务状态"
    echo "  $0 --force     # 强制停止"
}

# 强制停止
force_stop() {
    print_info "=== 强制停止所有相关进程 ==="
    
    local pids=$(pgrep -f "python.*main.py" || true)
    
    if [ -n "$pids" ]; then
        print_info "强制终止进程: $pids"
        pkill -9 -f "python.*main.py" || true
        sleep 2
        print_success "所有进程已强制终止"
    else
        print_info "未发现相关进程"
    fi
    
    # 清理PID文件
    if [ -f "service.pid" ]; then
        rm -f service.pid
        print_info "已删除PID文件"
    fi
}

# 主函数
main() {
    case "${1:-}" in
        --help|-h)
            show_help
            exit 0
            ;;
        --status|-s)
            show_status
            exit 0
            ;;
        --force|-f)
            force_stop
            exit 0
            ;;
        "")
            stop_service
            ;;
        *)
            print_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
