"""
邮件转发服务主逻辑
负责协调邮件接收和转发流程
"""

import time
import signal
import logging
import threading
from typing import Optional
from datetime import datetime

from config_manager import config_manager, AppConfig
from email_receiver import EmailReceiver
from email_forwarder import EmailForwarder


class EmailForwardService:
    """邮件转发服务"""
    
    def __init__(self):
        self.config: Optional[AppConfig] = None
        self.receiver: Optional[EmailReceiver] = None
        self.forwarder: Optional[EmailForwarder] = None
        self.logger = logging.getLogger(__name__)
        self.running = False
        self.service_thread: Optional[threading.Thread] = None
        
        # 注册信号处理器
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def initialize(self) -> bool:
        """初始化服务"""
        try:
            # 加载配置
            self.config = config_manager.load_config()
            self.logger.info("配置加载成功")
            
            # 初始化邮件接收器
            self.receiver = EmailReceiver(self.config.email_163)
            
            # 初始化邮件转发器
            self.forwarder = EmailForwarder(self.config.email_163)
            
            self.logger.info("服务初始化完成")
            return True
            
        except Exception as e:
            self.logger.error(f"服务初始化失败: {e}")
            return False
    
    def start(self) -> bool:
        """启动服务"""
        if not self.initialize():
            return False
        
        if self.running:
            self.logger.warning("服务已在运行中")
            return True
        
        try:
            # 测试连接
            if not self._test_connections():
                return False
            
            self.running = True
            self.logger.info("邮件转发服务启动成功")
            
            # 启动服务线程
            self.service_thread = threading.Thread(target=self._service_loop, daemon=True)
            self.service_thread.start()
            
            return True
            
        except Exception as e:
            self.logger.error(f"启动服务失败: {e}")
            return False
    
    def stop(self) -> None:
        """停止服务"""
        if not self.running:
            return
        
        self.logger.info("正在停止邮件转发服务...")
        self.running = False
        
        # 等待服务线程结束
        if self.service_thread and self.service_thread.is_alive():
            self.service_thread.join(timeout=10)
        
        # 断开连接
        if self.receiver:
            self.receiver.disconnect()
        if self.forwarder:
            self.forwarder.disconnect()
        
        self.logger.info("邮件转发服务已停止")
    
    def _test_connections(self) -> bool:
        """测试邮箱连接"""
        self.logger.info("测试邮箱连接...")
        
        # 测试IMAP连接
        if not self.receiver.connect():
            self.logger.error("IMAP连接测试失败")
            return False
        
        # 测试SMTP连接
        if not self.forwarder.connect():
            self.logger.error("SMTP连接测试失败")
            return False
        
        self.logger.info("邮箱连接测试成功")
        return True
    
    def _service_loop(self) -> None:
        """服务主循环"""
        self.logger.info(f"开始监控邮件，检查间隔: {self.config.service.check_interval}秒")
        
        while self.running:
            try:
                self._process_emails()
                
                # 等待下次检查
                for _ in range(self.config.service.check_interval):
                    if not self.running:
                        break
                    time.sleep(1)
                    
            except Exception as e:
                self.logger.error(f"处理邮件时出错: {e}")
                # 出错后等待一段时间再继续
                time.sleep(30)
    
    def _process_emails(self) -> None:
        """处理邮件"""
        try:
            # 获取新邮件
            emails = self.receiver.get_new_emails(self.config.service.max_emails_per_check)
            
            if not emails:
                self.logger.debug("没有新邮件")
                return
            
            self.logger.info(f"发现 {len(emails)} 封新邮件")
            
            # 处理每封邮件
            for email_info in emails:
                self._process_single_email(email_info)
                
        except Exception as e:
            self.logger.error(f"获取邮件失败: {e}")
    
    def _process_single_email(self, email_info) -> None:
        """处理单封邮件"""
        try:
            self.logger.info(f"处理邮件: {email_info.subject} (发件人: {email_info.sender})")
            
            # 检查是否应该转发
            if not self.receiver.should_forward_email(email_info, self.config.forward.rules):
                self.logger.info(f"邮件不符合转发规则，跳过: {email_info.subject}")
                if self.config.service.mark_as_read:
                    self.receiver.mark_as_read(email_info.uid)
                return
            
            # 转发邮件
            success = self.forwarder.forward_email(email_info, self.config.forward.target_emails)
            
            if success:
                self.logger.info(f"邮件转发成功: {email_info.subject}")
                # 标记为已读
                if self.config.service.mark_as_read:
                    self.receiver.mark_as_read(email_info.uid)
            else:
                self.logger.error(f"邮件转发失败: {email_info.subject}")
                
        except Exception as e:
            self.logger.error(f"处理邮件失败: {e}")
    
    def send_test_email(self) -> bool:
        """发送测试邮件"""
        if not self.forwarder:
            if not self.initialize():
                return False
        
        self.logger.info("发送测试邮件...")
        
        success_count = 0
        for target_email in self.config.forward.target_emails:
            if self.forwarder.send_test_email(target_email):
                success_count += 1
        
        if success_count > 0:
            self.logger.info(f"测试邮件发送成功，共发送到 {success_count} 个地址")
            return True
        else:
            self.logger.error("所有测试邮件发送失败")
            return False

    def send_manual_email(self, subject: str, content: str) -> bool:
        """手动发送邮件"""
        if not self.forwarder:
            if not self.initialize():
                return False

        try:
            self.logger.info(f"手动发送邮件: {subject}")

            # 创建一个模拟的EmailInfo对象
            from email_receiver import EmailInfo
            from datetime import datetime

            email_info = EmailInfo(
                uid=f"manual-{datetime.now().strftime('%Y%m%d%H%M%S')}",
                subject=subject,
                sender="手动发送",
                recipients=self.config.forward.target_emails,
                date=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                content_text=content,
                content_html=f"<p>{content.replace(chr(10), '<br>')}</p>",
                attachments=[],
                raw_message=b""
            )

            # 使用转发器发送邮件
            success = self.forwarder.forward_email(email_info, self.config.forward.target_emails)
            if success:
                self.logger.info(f"手动邮件发送成功，共发送到 {len(self.config.forward.target_emails)} 个地址")
                return True
            else:
                self.logger.error("手动邮件发送失败")
                return False

        except Exception as e:
            self.logger.error(f"手动发送邮件时出错: {e}")
            return False
    
    def get_status(self) -> dict:
        """获取服务状态"""
        return {
            'running': self.running,
            'config_loaded': self.config is not None,
            'receiver_connected': self.receiver and hasattr(self.receiver, 'imap_conn') and self.receiver.imap_conn,
            'forwarder_connected': self.forwarder and hasattr(self.forwarder, 'smtp_conn') and self.forwarder.smtp_conn,
            'check_interval': self.config.service.check_interval if self.config else None,
            'target_emails': self.config.forward.target_emails if self.config else None,
            'current_time': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        self.logger.info(f"收到信号 {signum}，正在停止服务...")
        self.stop()


# 全局服务实例
email_service = EmailForwardService()
