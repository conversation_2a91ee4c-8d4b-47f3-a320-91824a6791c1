2025-07-28 20:21:05 - root - [32mIN<PERSON>O[0m - 日志系统初始化完成
2025-07-28 20:21:05 - root - [32mIN<PERSON>O[0m - 日志级别已设置为: DEBUG
2025-07-28 20:21:05 - __main__ - [32mINFO[0m - === 163邮箱转发服务启动 ===
2025-07-28 20:21:05 - __main__ - [32mINFO[0m - 配置文件: config.yaml
2025-07-28 20:21:05 - __main__ - [32mINFO[0m - 163邮箱: <EMAIL>
2025-07-28 20:21:05 - __main__ - [32mINFO[0m - 转发目标: <EMAIL>, <EMAIL>
2025-07-28 20:21:05 - __main__ - [32mINFO[0m - 运行模式: 邮件转发服务
2025-07-28 20:21:05 - email_service - [32m<PERSON>FO[0m - 配置加载成功
2025-07-28 20:21:05 - email_service - [32mINFO[0m - 服务初始化完成
2025-07-28 20:21:05 - email_service - [32mIN<PERSON>O[0m - 测试邮箱连接...
2025-07-28 20:21:05 - email_receiver - [32mINFO[0m - 连接到IMAP服务器: imap.163.com:993
2025-07-28 20:21:09 - email_receiver - [32mINFO[0m - IMAP连接成功
2025-07-28 20:21:09 - email_forwarder - [32mINFO[0m - 连接到SMTP服务器: smtp.163.com:465
2025-07-28 20:21:14 - email_forwarder - [32mINFO[0m - SMTP连接成功
2025-07-28 20:21:14 - email_service - [32mINFO[0m - 邮箱连接测试成功
2025-07-28 20:21:14 - email_service - [32mINFO[0m - 邮件转发服务启动成功
2025-07-28 20:21:14 - email_service - [32mINFO[0m - 开始监控邮件，检查间隔: 60秒
2025-07-28 20:21:14 - __main__ - [32mINFO[0m - 服务正在运行，按 Ctrl+C 停止...
2025-07-28 20:21:15 - email_receiver - [31mERROR[0m - 获取邮件失败: command SEARCH illegal in state AUTH, only allowed in states SELECTED
2025-07-28 20:21:15 - email_service - [36mDEBUG[0m - 没有新邮件
2025-07-28 20:21:42 - email_service - [32mINFO[0m - 收到信号 2，正在停止服务...
2025-07-28 20:21:42 - email_service - [32mINFO[0m - 正在停止邮件转发服务...
2025-07-28 20:21:43 - email_receiver - [31mERROR[0m - 断开IMAP连接时出错: command CLOSE illegal in state AUTH, only allowed in states SELECTED
2025-07-28 20:21:43 - email_forwarder - [31mERROR[0m - 断开SMTP连接时出错: Connection unexpectedly closed
2025-07-28 20:21:43 - email_service - [32mINFO[0m - 邮件转发服务已停止
2025-07-28 20:21:43 - __main__ - [32mINFO[0m - === 163邮箱转发服务已停止 ===
2025-07-28 20:22:14 - root - [32mINFO[0m - 日志系统初始化完成
2025-07-28 20:22:14 - __main__ - [32mINFO[0m - === 163邮箱转发服务启动 ===
2025-07-28 20:22:14 - __main__ - [32mINFO[0m - 配置文件: config.yaml
2025-07-28 20:22:14 - __main__ - [32mINFO[0m - 163邮箱: <EMAIL>
2025-07-28 20:22:14 - __main__ - [32mINFO[0m - 转发目标: <EMAIL>
2025-07-28 20:22:14 - __main__ - [32mINFO[0m - 运行模式: 测试邮件发送
2025-07-28 20:22:14 - email_service - [32mINFO[0m - 配置加载成功
2025-07-28 20:22:14 - email_service - [32mINFO[0m - 服务初始化完成
2025-07-28 20:22:14 - email_service - [32mINFO[0m - 发送测试邮件...
2025-07-28 20:22:14 - email_forwarder - [32mINFO[0m - 连接到SMTP服务器: smtp.163.com:465
2025-07-28 20:22:16 - email_forwarder - [32mINFO[0m - SMTP连接成功
2025-07-28 20:22:18 - email_forwarder - [32mINFO[0m - 测试邮件发送成功: <EMAIL>
2025-07-28 20:22:18 - email_service - [32mINFO[0m - 测试邮件发送成功，共发送到 1 个地址
2025-07-28 20:22:18 - __main__ - [32mINFO[0m - 测试邮件发送成功
2025-07-28 20:22:28 - root - [32mINFO[0m - 日志系统初始化完成
2025-07-28 20:22:28 - root - [32mINFO[0m - 日志级别已设置为: INFO
2025-07-28 20:22:28 - __main__ - [32mINFO[0m - === 163邮箱转发服务启动 ===
2025-07-28 20:22:28 - __main__ - [32mINFO[0m - 配置文件: config.yaml
2025-07-28 20:22:28 - __main__ - [32mINFO[0m - 163邮箱: <EMAIL>
2025-07-28 20:22:28 - __main__ - [32mINFO[0m - 转发目标: <EMAIL>
2025-07-28 20:22:28 - __main__ - [32mINFO[0m - 运行模式: 邮件转发服务
2025-07-28 20:22:28 - email_service - [32mINFO[0m - 配置加载成功
2025-07-28 20:22:28 - email_service - [32mINFO[0m - 服务初始化完成
2025-07-28 20:22:28 - email_service - [32mINFO[0m - 测试邮箱连接...
2025-07-28 20:22:28 - email_receiver - [32mINFO[0m - 连接到IMAP服务器: imap.163.com:993
2025-07-28 20:22:32 - email_receiver - [32mINFO[0m - IMAP连接成功
2025-07-28 20:22:32 - email_forwarder - [32mINFO[0m - 连接到SMTP服务器: smtp.163.com:465
2025-07-28 20:22:35 - email_forwarder - [32mINFO[0m - SMTP连接成功
2025-07-28 20:22:35 - email_service - [32mINFO[0m - 邮箱连接测试成功
2025-07-28 20:22:35 - email_service - [32mINFO[0m - 邮件转发服务启动成功
2025-07-28 20:22:35 - email_service - [32mINFO[0m - 开始监控邮件，检查间隔: 60秒
2025-07-28 20:22:35 - __main__ - [32mINFO[0m - 服务正在运行，按 Ctrl+C 停止...
2025-07-28 20:22:35 - email_receiver - [31mERROR[0m - 选择收件箱失败: NO
2025-07-28 20:22:35 - email_receiver - [31mERROR[0m - 断开IMAP连接时出错: command CLOSE illegal in state AUTH, only allowed in states SELECTED
2025-07-28 20:22:35 - email_receiver - [32mINFO[0m - 连接到IMAP服务器: imap.163.com:993
2025-07-28 20:22:38 - email_receiver - [32mINFO[0m - IMAP连接成功
2025-07-28 20:22:39 - email_receiver - [31mERROR[0m - 重新连接后仍无法选择收件箱: NO
2025-07-28 20:23:07 - email_service - [32mINFO[0m - 收到信号 2，正在停止服务...
2025-07-28 20:23:07 - email_service - [32mINFO[0m - 正在停止邮件转发服务...
2025-07-28 20:23:08 - email_receiver - [31mERROR[0m - 断开IMAP连接时出错: command CLOSE illegal in state AUTH, only allowed in states SELECTED
2025-07-28 20:23:08 - email_forwarder - [31mERROR[0m - 断开SMTP连接时出错: Connection unexpectedly closed
2025-07-28 20:23:08 - email_service - [32mINFO[0m - 邮件转发服务已停止
2025-07-28 20:23:08 - __main__ - [32mINFO[0m - === 163邮箱转发服务已停止 ===
2025-07-28 20:23:51 - root - [32mINFO[0m - 日志系统初始化完成
2025-07-28 20:23:51 - root - [32mINFO[0m - 日志级别已设置为: DEBUG
2025-07-28 20:23:51 - __main__ - [32mINFO[0m - === 163邮箱转发服务启动 ===
2025-07-28 20:23:51 - __main__ - [32mINFO[0m - 配置文件: config.yaml
2025-07-28 20:23:51 - __main__ - [32mINFO[0m - 163邮箱: <EMAIL>
2025-07-28 20:23:51 - __main__ - [32mINFO[0m - 转发目标: <EMAIL>
2025-07-28 20:23:51 - __main__ - [32mINFO[0m - 运行模式: 邮件转发服务
2025-07-28 20:23:51 - email_service - [32mINFO[0m - 配置加载成功
2025-07-28 20:23:51 - email_service - [32mINFO[0m - 服务初始化完成
2025-07-28 20:23:51 - email_service - [32mINFO[0m - 测试邮箱连接...
2025-07-28 20:23:51 - email_receiver - [32mINFO[0m - 连接到IMAP服务器: imap.163.com:993
2025-07-28 20:23:55 - email_receiver - [32mINFO[0m - IMAP连接成功
2025-07-28 20:23:55 - email_forwarder - [32mINFO[0m - 连接到SMTP服务器: smtp.163.com:465
2025-07-28 20:24:07 - email_forwarder - [32mINFO[0m - SMTP连接成功
2025-07-28 20:24:07 - email_service - [32mINFO[0m - 邮箱连接测试成功
2025-07-28 20:24:07 - email_service - [32mINFO[0m - 邮件转发服务启动成功
2025-07-28 20:24:07 - email_service - [32mINFO[0m - 开始监控邮件，检查间隔: 60秒
2025-07-28 20:24:07 - __main__ - [32mINFO[0m - 服务正在运行，按 Ctrl+C 停止...
2025-07-28 20:24:07 - email_receiver - [36mDEBUG[0m - 列出可用的邮箱文件夹...
2025-07-28 20:24:08 - email_receiver - [36mDEBUG[0m - 可用文件夹: [b'() "/" "INBOX"', b'(\\Drafts) "/" "&g0l6P3ux-"', b'(\\Sent) "/" "&XfJT0ZAB-"', b'(\\Trash) "/" "&XfJSIJZk-"', b'(\\Junk) "/" "&V4NXPpCuTvY-"', b'() "/" "&dcVr0mWHTvZZOQ-"']
2025-07-28 20:24:08 - email_receiver - [36mDEBUG[0m - 选择邮箱 INBOX 失败: NO
2025-07-28 20:24:09 - email_receiver - [36mDEBUG[0m - 选择邮箱 Inbox 失败: NO
2025-07-28 20:24:09 - email_receiver - [36mDEBUG[0m - 尝试选择邮箱 收件箱 时出错: 'ascii' codec can't encode characters in position 0-2: ordinal not in range(128)
2025-07-28 20:24:09 - email_receiver - [36mDEBUG[0m - 选择邮箱 inbox 失败: NO
2025-07-28 20:24:09 - email_receiver - [31mERROR[0m - 无法选择任何收件箱
2025-07-28 20:24:09 - email_receiver - [31mERROR[0m - 断开IMAP连接时出错: command CLOSE illegal in state AUTH, only allowed in states SELECTED
2025-07-28 20:24:09 - email_receiver - [32mINFO[0m - 连接到IMAP服务器: imap.163.com:993
2025-07-28 20:24:14 - email_receiver - [32mINFO[0m - IMAP连接成功
2025-07-28 20:24:22 - email_receiver - [31mERROR[0m - 重新连接后仍无法选择收件箱: NO
2025-07-28 20:24:22 - email_service - [36mDEBUG[0m - 没有新邮件
2025-07-28 20:24:29 - email_service - [32mINFO[0m - 收到信号 2，正在停止服务...
2025-07-28 20:24:29 - email_service - [32mINFO[0m - 正在停止邮件转发服务...
2025-07-28 20:24:29 - email_receiver - [31mERROR[0m - 断开IMAP连接时出错: command CLOSE illegal in state AUTH, only allowed in states SELECTED
2025-07-28 20:24:29 - email_forwarder - [31mERROR[0m - 断开SMTP连接时出错: Connection unexpectedly closed
2025-07-28 20:24:29 - email_service - [32mINFO[0m - 邮件转发服务已停止
2025-07-28 20:24:29 - __main__ - [32mINFO[0m - === 163邮箱转发服务已停止 ===
2025-07-28 20:29:18 - root - [32mINFO[0m - 日志系统初始化完成
2025-07-28 20:29:18 - root - [32mINFO[0m - 日志级别已设置为: DEBUG
2025-07-28 20:29:18 - __main__ - [32mINFO[0m - === 163邮箱转发服务启动 ===
2025-07-28 20:29:18 - __main__ - [32mINFO[0m - 配置文件: config.yaml
2025-07-28 20:29:18 - __main__ - [32mINFO[0m - 163邮箱: <EMAIL>
2025-07-28 20:29:18 - __main__ - [32mINFO[0m - 转发目标: <EMAIL>
2025-07-28 20:29:18 - __main__ - [32mINFO[0m - 运行模式: 邮件转发服务
2025-07-28 20:29:18 - email_service - [32mINFO[0m - 配置加载成功
2025-07-28 20:29:18 - email_service - [32mINFO[0m - 服务初始化完成
2025-07-28 20:29:18 - email_service - [32mINFO[0m - 测试邮箱连接...
2025-07-28 20:29:18 - email_receiver - [32mINFO[0m - 连接到IMAP服务器: imap.163.com:993
2025-07-28 20:29:22 - email_receiver - [32mINFO[0m - IMAP连接成功
2025-07-28 20:29:22 - email_forwarder - [32mINFO[0m - 连接到SMTP服务器: smtp.163.com:465
2025-07-28 20:29:26 - email_forwarder - [32mINFO[0m - SMTP连接成功
2025-07-28 20:29:26 - email_service - [32mINFO[0m - 邮箱连接测试成功
2025-07-28 20:29:26 - email_service - [32mINFO[0m - 邮件转发服务启动成功
2025-07-28 20:29:26 - email_service - [32mINFO[0m - 开始监控邮件，检查间隔: 60秒
2025-07-28 20:29:26 - __main__ - [32mINFO[0m - 服务正在运行，按 Ctrl+C 停止...
2025-07-28 20:29:26 - email_receiver - [36mDEBUG[0m - 列出可用的邮箱文件夹...
2025-07-28 20:29:27 - email_receiver - [36mDEBUG[0m - 可用文件夹: [b'() "/" "INBOX"', b'(\\Drafts) "/" "&g0l6P3ux-"', b'(\\Sent) "/" "&XfJT0ZAB-"', b'(\\Trash) "/" "&XfJSIJZk-"', b'(\\Junk) "/" "&V4NXPpCuTvY-"', b'() "/" "&dcVr0mWHTvZZOQ-"']
2025-07-28 20:29:27 - email_receiver - [36mDEBUG[0m - 选择邮箱 INBOX 失败: NO, 响应: [b'SELECT Unsafe Login. <NAME_EMAIL> for help']
2025-07-28 20:29:28 - email_receiver - [36mDEBUG[0m - 选择邮箱 Inbox 失败: NO, 响应: [b'SELECT Unsafe Login. <NAME_EMAIL> for help']
2025-07-28 20:29:28 - email_receiver - [36mDEBUG[0m - 尝试选择邮箱 收件箱 时出错: 'ascii' codec can't encode characters in position 0-2: ordinal not in range(128)
2025-07-28 20:29:28 - email_receiver - [36mDEBUG[0m - 选择邮箱 inbox 失败: NO, 响应: [b'SELECT Unsafe Login. <NAME_EMAIL> for help']
2025-07-28 20:29:28 - email_receiver - [36mDEBUG[0m - 尝试使用只读模式访问INBOX...
2025-07-28 20:29:28 - email_receiver - [36mDEBUG[0m - 只读模式选择INBOX时出错: Unknown IMAP4 command: 'examine'
2025-07-28 20:29:28 - email_receiver - [31mERROR[0m - 无法选择任何收件箱
2025-07-28 20:29:28 - email_receiver - [31mERROR[0m - 断开IMAP连接时出错: command CLOSE illegal in state AUTH, only allowed in states SELECTED
2025-07-28 20:29:28 - email_receiver - [32mINFO[0m - 连接到IMAP服务器: imap.163.com:993
2025-07-28 20:29:32 - email_receiver - [32mINFO[0m - IMAP连接成功
2025-07-28 20:29:34 - email_receiver - [31mERROR[0m - 重新连接后仍无法选择收件箱: NO
2025-07-28 20:29:34 - email_service - [36mDEBUG[0m - 没有新邮件
2025-07-28 20:29:57 - email_service - [32mINFO[0m - 收到信号 2，正在停止服务...
2025-07-28 20:29:57 - email_service - [32mINFO[0m - 正在停止邮件转发服务...
2025-07-28 20:29:58 - email_receiver - [31mERROR[0m - 断开IMAP连接时出错: command CLOSE illegal in state AUTH, only allowed in states SELECTED
2025-07-28 20:29:58 - email_forwarder - [31mERROR[0m - 断开SMTP连接时出错: Connection unexpectedly closed
2025-07-28 20:29:58 - email_service - [32mINFO[0m - 邮件转发服务已停止
2025-07-28 20:29:58 - __main__ - [32mINFO[0m - === 163邮箱转发服务已停止 ===
2025-07-28 20:32:52 - root - [32mINFO[0m - 日志系统初始化完成
2025-07-28 20:32:52 - __main__ - [32mINFO[0m - === 163邮箱转发服务启动 ===
2025-07-28 20:32:52 - __main__ - [32mINFO[0m - 配置文件: config.yaml
2025-07-28 20:32:52 - __main__ - [32mINFO[0m - 163邮箱: <EMAIL>
2025-07-28 20:32:52 - __main__ - [32mINFO[0m - 转发目标: <EMAIL>
2025-07-28 20:32:52 - __main__ - [32mINFO[0m - 运行模式: 手动发送邮件
2025-07-28 20:32:52 - email_service - [32mINFO[0m - 配置加载成功
2025-07-28 20:32:52 - email_service - [32mINFO[0m - 服务初始化完成
2025-07-28 20:32:52 - email_service - [32mINFO[0m - 手动发送邮件: 测试邮件主题
2025-07-28 20:32:52 - email_service - [31mERROR[0m - 手动发送邮件时出错: __init__() got an unexpected keyword argument 'message_id'
2025-07-28 20:32:52 - __main__ - [31mERROR[0m - 手动邮件发送失败
2025-07-28 20:34:50 - root - [32mINFO[0m - 日志系统初始化完成
2025-07-28 20:34:50 - __main__ - [32mINFO[0m - === 163邮箱转发服务启动 ===
2025-07-28 20:34:50 - __main__ - [32mINFO[0m - 配置文件: config.yaml
2025-07-28 20:34:50 - __main__ - [32mINFO[0m - 163邮箱: <EMAIL>
2025-07-28 20:34:50 - __main__ - [32mINFO[0m - 转发目标: <EMAIL>
2025-07-28 20:34:50 - __main__ - [32mINFO[0m - 运行模式: 邮件转发服务
2025-07-28 20:34:50 - email_service - [32mINFO[0m - 配置加载成功
2025-07-28 20:34:50 - email_service - [32mINFO[0m - 服务初始化完成
2025-07-28 20:34:50 - email_service - [32mINFO[0m - 测试邮箱连接...
2025-07-28 20:34:50 - email_receiver - [32mINFO[0m - 连接到IMAP服务器: imap.163.com:993
2025-07-28 20:34:54 - email_receiver - [32mINFO[0m - IMAP连接成功
2025-07-28 20:34:54 - email_forwarder - [32mINFO[0m - 连接到SMTP服务器: smtp.163.com:465
2025-07-28 20:34:58 - email_forwarder - [32mINFO[0m - SMTP连接成功
2025-07-28 20:34:58 - email_service - [32mINFO[0m - 邮箱连接测试成功
2025-07-28 20:34:58 - email_service - [32mINFO[0m - 邮件转发服务启动成功
2025-07-28 20:34:58 - email_service - [32mINFO[0m - 开始监控邮件，检查间隔: 60秒
2025-07-28 20:34:58 - __main__ - [32mINFO[0m - 服务正在运行，按 Ctrl+C 停止...
2025-07-28 20:35:01 - email_receiver - [31mERROR[0m - 无法选择任何收件箱
2025-07-28 20:35:01 - email_receiver - [31mERROR[0m - 断开IMAP连接时出错: command CLOSE illegal in state AUTH, only allowed in states SELECTED
2025-07-28 20:35:01 - email_receiver - [32mINFO[0m - 连接到IMAP服务器: imap.163.com:993
2025-07-28 20:35:22 - email_service - [32mINFO[0m - 收到信号 2，正在停止服务...
2025-07-28 20:35:22 - email_service - [32mINFO[0m - 正在停止邮件转发服务...
2025-07-28 20:35:24 - email_service - [32mINFO[0m - 收到信号 2，正在停止服务...
2025-07-28 20:35:25 - email_service - [32mINFO[0m - 收到信号 2，正在停止服务...
2025-07-28 20:35:25 - email_service - [32mINFO[0m - 收到信号 2，正在停止服务...
2025-07-28 20:35:25 - email_service - [32mINFO[0m - 收到信号 2，正在停止服务...
2025-07-28 20:35:32 - email_forwarder - [31mERROR[0m - 断开SMTP连接时出错: Connection unexpectedly closed
2025-07-28 20:35:32 - email_service - [32mINFO[0m - 邮件转发服务已停止
2025-07-28 20:35:32 - __main__ - [32mINFO[0m - === 163邮箱转发服务已停止 ===
2025-07-28 20:43:31 - root - [32mINFO[0m - 日志系统初始化完成
2025-07-28 20:43:31 - __main__ - [32mINFO[0m - === 163邮箱转发服务启动 ===
2025-07-28 20:43:31 - __main__ - [32mINFO[0m - 配置文件: config.yaml
2025-07-28 20:43:31 - __main__ - [32mINFO[0m - 163邮箱: <EMAIL>
2025-07-28 20:43:31 - __main__ - [32mINFO[0m - 转发目标: <EMAIL>
2025-07-28 20:43:31 - __main__ - [32mINFO[0m - 运行模式: 测试邮件发送
2025-07-28 20:43:31 - email_service - [32mINFO[0m - 配置加载成功
2025-07-28 20:43:31 - email_service - [32mINFO[0m - 服务初始化完成
2025-07-28 20:43:31 - email_service - [32mINFO[0m - 发送测试邮件...
2025-07-28 20:43:31 - email_forwarder - [32mINFO[0m - 连接到SMTP服务器: smtp.163.com:465
2025-07-28 20:43:32 - email_forwarder - [32mINFO[0m - SMTP连接成功
2025-07-28 20:43:32 - email_forwarder - [32mINFO[0m - 测试邮件发送成功: <EMAIL>
2025-07-28 20:43:32 - email_service - [32mINFO[0m - 测试邮件发送成功，共发送到 1 个地址
2025-07-28 20:43:32 - __main__ - [32mINFO[0m - 测试邮件发送成功
2025-07-28 20:43:44 - root - [32mINFO[0m - 日志系统初始化完成
2025-07-28 20:43:44 - __main__ - [32mINFO[0m - === 163邮箱转发服务启动 ===
2025-07-28 20:43:44 - __main__ - [32mINFO[0m - 配置文件: config.yaml
2025-07-28 20:43:44 - __main__ - [32mINFO[0m - 163邮箱: <EMAIL>
2025-07-28 20:43:44 - __main__ - [32mINFO[0m - 转发目标: <EMAIL>
2025-07-28 20:43:44 - __main__ - [32mINFO[0m - 运行模式: 状态查看
2025-07-28 20:44:08 - root - [32mINFO[0m - 日志系统初始化完成
2025-07-28 20:44:08 - __main__ - [32mINFO[0m - === 163邮箱转发服务启动 ===
2025-07-28 20:44:08 - __main__ - [32mINFO[0m - 配置文件: config.yaml
2025-07-28 20:44:08 - __main__ - [32mINFO[0m - 163邮箱: <EMAIL>
2025-07-28 20:44:08 - __main__ - [32mINFO[0m - 转发目标: <EMAIL>
2025-07-28 20:44:08 - __main__ - [32mINFO[0m - 运行模式: 邮件转发服务
2025-07-28 20:44:08 - email_service - [32mINFO[0m - 配置加载成功
2025-07-28 20:44:08 - email_service - [32mINFO[0m - 服务初始化完成
2025-07-28 20:44:08 - email_service - [32mINFO[0m - 测试邮箱连接...
2025-07-28 20:44:08 - email_receiver - [32mINFO[0m - 连接到IMAP服务器: imap.163.com:993
2025-07-28 20:44:08 - email_receiver - [32mINFO[0m - IMAP连接成功
2025-07-28 20:44:08 - email_forwarder - [32mINFO[0m - 连接到SMTP服务器: smtp.163.com:465
2025-07-28 20:44:08 - email_forwarder - [32mINFO[0m - SMTP连接成功
2025-07-28 20:44:08 - email_service - [32mINFO[0m - 邮箱连接测试成功
2025-07-28 20:44:08 - email_service - [32mINFO[0m - 邮件转发服务启动成功
2025-07-28 20:44:08 - email_service - [32mINFO[0m - 开始监控邮件，检查间隔: 60秒
2025-07-28 20:44:08 - __main__ - [32mINFO[0m - 服务正在运行，按 Ctrl+C 停止...
2025-07-28 20:44:08 - email_receiver - [31mERROR[0m - 无法选择任何收件箱
2025-07-28 20:44:08 - email_receiver - [31mERROR[0m - 断开IMAP连接时出错: command CLOSE illegal in state AUTH, only allowed in states SELECTED
2025-07-28 20:44:08 - email_receiver - [32mINFO[0m - 连接到IMAP服务器: imap.163.com:993
2025-07-28 20:44:09 - email_receiver - [32mINFO[0m - IMAP连接成功
2025-07-28 20:44:09 - email_receiver - [31mERROR[0m - 重新连接后仍无法选择收件箱: NO
2025-07-28 20:45:47 - root - [32mINFO[0m - 日志系统初始化完成
2025-07-28 20:45:47 - __main__ - [32mINFO[0m - === 163邮箱转发服务启动 ===
2025-07-28 20:45:47 - __main__ - [32mINFO[0m - 配置文件: config.yaml
2025-07-28 20:45:47 - __main__ - [32mINFO[0m - 163邮箱: <EMAIL>
2025-07-28 20:45:47 - __main__ - [32mINFO[0m - 转发目标: <EMAIL>
2025-07-28 20:45:47 - __main__ - [32mINFO[0m - 运行模式: 手动发送邮件
2025-07-28 20:45:47 - email_service - [32mINFO[0m - 配置加载成功
2025-07-28 20:45:47 - email_service - [32mINFO[0m - 服务初始化完成
2025-07-28 20:45:47 - email_service - [32mINFO[0m - 手动发送邮件: 测试邮件主题
2025-07-28 20:45:47 - email_service - [31mERROR[0m - 手动发送邮件时出错: EmailInfo.__init__() got an unexpected keyword argument 'message_id'
2025-07-28 20:45:47 - __main__ - [31mERROR[0m - 手动邮件发送失败
2025-07-28 20:47:02 - root - [32mINFO[0m - 日志系统初始化完成
2025-07-28 20:47:02 - __main__ - [32mINFO[0m - === 163邮箱转发服务启动 ===
2025-07-28 20:47:02 - __main__ - [32mINFO[0m - 配置文件: config.yaml
2025-07-28 20:47:02 - __main__ - [32mINFO[0m - 163邮箱: <EMAIL>
2025-07-28 20:47:02 - __main__ - [32mINFO[0m - 转发目标: <EMAIL>
2025-07-28 20:47:02 - __main__ - [32mINFO[0m - 运行模式: 手动发送邮件
2025-07-28 20:47:02 - email_service - [32mINFO[0m - 配置加载成功
2025-07-28 20:47:02 - email_service - [32mINFO[0m - 服务初始化完成
2025-07-28 20:47:02 - email_service - [32mINFO[0m - 手动发送邮件: 测试邮件主题
2025-07-28 20:47:02 - email_service - [31mERROR[0m - 手动发送邮件时出错: EmailForwarder.forward_email() missing 1 required positional argument: 'target_emails'
2025-07-28 20:47:02 - __main__ - [31mERROR[0m - 手动邮件发送失败
2025-07-28 20:48:11 - root - [32mINFO[0m - 日志系统初始化完成
2025-07-28 20:48:11 - __main__ - [32mINFO[0m - === 163邮箱转发服务启动 ===
2025-07-28 20:48:11 - __main__ - [32mINFO[0m - 配置文件: config.yaml
2025-07-28 20:48:11 - __main__ - [32mINFO[0m - 163邮箱: <EMAIL>
2025-07-28 20:48:11 - __main__ - [32mINFO[0m - 转发目标: <EMAIL>
2025-07-28 20:48:11 - __main__ - [32mINFO[0m - 运行模式: 手动发送邮件
2025-07-28 20:48:11 - email_service - [32mINFO[0m - 配置加载成功
2025-07-28 20:48:11 - email_service - [32mINFO[0m - 服务初始化完成
2025-07-28 20:48:11 - email_service - [32mINFO[0m - 手动发送邮件: 测试邮件主题
2025-07-28 20:48:11 - email_forwarder - [32mINFO[0m - 连接到SMTP服务器: smtp.163.com:465
2025-07-28 20:48:12 - email_forwarder - [32mINFO[0m - SMTP连接成功
2025-07-28 20:48:12 - email_forwarder - [32mINFO[0m - 邮件成功转发到: <EMAIL>
2025-07-28 20:48:12 - email_service - [32mINFO[0m - 手动邮件发送成功，共发送到 1 个地址
2025-07-28 20:48:12 - __main__ - [32mINFO[0m - 手动邮件发送成功
