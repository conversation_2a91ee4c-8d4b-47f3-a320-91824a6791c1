"""
邮件接收模块
负责连接163邮箱IMAP服务器，读取和解析新邮件
"""

import imaplib
import email
import logging
from typing import List, Tuple, Optional
from email.message import EmailMessage
from email.header import decode_header
from dataclasses import dataclass
from config_manager import EmailConfig, ForwardRules


@dataclass
class EmailInfo:
    """邮件信息"""
    uid: str
    subject: str
    sender: str
    recipients: List[str]
    date: str
    content_text: str
    content_html: str
    attachments: List[Tuple[str, bytes]]  # (filename, content)
    raw_message: bytes


class EmailReceiver:
    """邮件接收器"""
    
    def __init__(self, email_config: EmailConfig):
        self.config = email_config
        self.imap_conn: Optional[imaplib.IMAP4_SSL] = None
        self.logger = logging.getLogger(__name__)
    
    def connect(self) -> bool:
        """连接到IMAP服务器"""
        try:
            self.logger.info(f"连接到IMAP服务器: {self.config.imap_server}:{self.config.imap_port}")
            self.imap_conn = imaplib.IMAP4_SSL(self.config.imap_server, self.config.imap_port)
            self.imap_conn.login(self.config.username, self.config.password)
            self.logger.info("IMAP连接成功")
            return True
        except Exception as e:
            self.logger.error(f"IMAP连接失败: {e}")
            return False
    
    def disconnect(self) -> None:
        """断开IMAP连接"""
        if self.imap_conn:
            try:
                self.imap_conn.close()
                self.imap_conn.logout()
                self.logger.info("IMAP连接已断开")
            except Exception as e:
                self.logger.error(f"断开IMAP连接时出错: {e}")
            finally:
                self.imap_conn = None
    
    def get_new_emails(self, max_count: int = 10) -> List[EmailInfo]:
        """获取新邮件"""
        if not self.imap_conn:
            if not self.connect():
                return []

        try:
            # 列出可用的邮箱文件夹
            self.logger.debug("列出可用的邮箱文件夹...")
            status, folders = self.imap_conn.list()
            if status == 'OK':
                self.logger.debug(f"可用文件夹: {folders}")

            # 尝试选择不同的收件箱名称
            inbox_names = ['INBOX', 'Inbox', '收件箱', 'inbox']
            selected = False

            for inbox_name in inbox_names:
                try:
                    status, response = self.imap_conn.select(inbox_name)
                    if status == 'OK':
                        self.logger.debug(f"成功选择邮箱: {inbox_name}, 响应: {response}")
                        selected = True
                        break
                    else:
                        self.logger.debug(f"选择邮箱 {inbox_name} 失败: {status}, 响应: {response}")
                except Exception as e:
                    self.logger.debug(f"尝试选择邮箱 {inbox_name} 时出错: {e}")

            # 如果标准方法失败，尝试使用examine（只读模式）
            if not selected:
                self.logger.debug("尝试使用只读模式访问INBOX...")
                try:
                    status, response = self.imap_conn.examine('INBOX')
                    if status == 'OK':
                        self.logger.debug(f"成功以只读模式选择INBOX: {response}")
                        selected = True
                    else:
                        self.logger.debug(f"只读模式选择INBOX失败: {status}, 响应: {response}")
                except Exception as e:
                    self.logger.debug(f"只读模式选择INBOX时出错: {e}")

            if not selected:
                self.logger.error("无法选择任何收件箱")
                # 尝试重新连接
                self.disconnect()
                if not self.connect():
                    return []
                # 再次尝试选择INBOX
                try:
                    status, count = self.imap_conn.select('INBOX')
                    if status != 'OK':
                        self.logger.error(f"重新连接后仍无法选择收件箱: {status}")
                        return []
                except Exception as e:
                    self.logger.error(f"重新连接后选择收件箱出错: {e}")
                    return []

            # 搜索未读邮件
            status, messages = self.imap_conn.search(None, 'UNSEEN')
            if status != 'OK':
                self.logger.error(f"搜索邮件失败: {status}")
                return []

            email_ids = messages[0].split()
            if not email_ids:
                self.logger.debug("没有新邮件")
                return []

            # 限制处理的邮件数量
            email_ids = email_ids[-max_count:]

            emails = []
            for email_id in email_ids:
                email_info = self._fetch_email(email_id)
                if email_info:
                    emails.append(email_info)

            self.logger.info(f"获取到 {len(emails)} 封新邮件")
            return emails

        except Exception as e:
            self.logger.error(f"获取邮件失败: {e}")
            # 连接可能已断开，清理连接状态
            self.disconnect()
            return []
    
    def _fetch_email(self, email_id: bytes) -> Optional[EmailInfo]:
        """获取单封邮件的详细信息"""
        try:
            # 获取邮件数据
            status, msg_data = self.imap_conn.fetch(email_id, '(RFC822)')
            if status != 'OK':
                return None
            
            raw_email = msg_data[0][1]
            email_message = email.message_from_bytes(raw_email)
            
            # 解析邮件头信息
            subject = self._decode_header(email_message.get('Subject', ''))
            sender = self._decode_header(email_message.get('From', ''))
            recipients = self._parse_recipients(email_message)
            date = email_message.get('Date', '')
            
            # 解析邮件内容
            content_text, content_html, attachments = self._parse_content(email_message)
            
            return EmailInfo(
                uid=email_id.decode(),
                subject=subject,
                sender=sender,
                recipients=recipients,
                date=date,
                content_text=content_text,
                content_html=content_html,
                attachments=attachments,
                raw_message=raw_email
            )
            
        except Exception as e:
            self.logger.error(f"解析邮件失败: {e}")
            return None
    
    def _decode_header(self, header: str) -> str:
        """解码邮件头"""
        if not header:
            return ""
        
        try:
            decoded_parts = decode_header(header)
            result = ""
            for part, encoding in decoded_parts:
                if isinstance(part, bytes):
                    if encoding:
                        result += part.decode(encoding)
                    else:
                        result += part.decode('utf-8', errors='ignore')
                else:
                    result += part
            return result
        except Exception as e:
            self.logger.warning(f"解码邮件头失败: {e}")
            return str(header)
    
    def _parse_recipients(self, email_message: email.message.EmailMessage) -> List[str]:
        """解析收件人列表"""
        recipients = []
        for field in ['To', 'Cc', 'Bcc']:
            value = email_message.get(field)
            if value:
                recipients.extend([addr.strip() for addr in value.split(',')])
        return recipients
    
    def _parse_content(self, email_message: email.message.EmailMessage) -> Tuple[str, str, List[Tuple[str, bytes]]]:
        """解析邮件内容"""
        content_text = ""
        content_html = ""
        attachments = []
        
        if email_message.is_multipart():
            for part in email_message.walk():
                content_type = part.get_content_type()
                content_disposition = str(part.get('Content-Disposition', ''))
                
                if 'attachment' in content_disposition:
                    # 处理附件
                    filename = part.get_filename()
                    if filename:
                        filename = self._decode_header(filename)
                        content = part.get_payload(decode=True)
                        if content:
                            attachments.append((filename, content))
                elif content_type == 'text/plain' and 'attachment' not in content_disposition:
                    # 处理纯文本内容
                    charset = part.get_content_charset() or 'utf-8'
                    content = part.get_payload(decode=True)
                    if content:
                        content_text += content.decode(charset, errors='ignore')
                elif content_type == 'text/html' and 'attachment' not in content_disposition:
                    # 处理HTML内容
                    charset = part.get_content_charset() or 'utf-8'
                    content = part.get_payload(decode=True)
                    if content:
                        content_html += content.decode(charset, errors='ignore')
        else:
            # 非多部分邮件
            content_type = email_message.get_content_type()
            charset = email_message.get_content_charset() or 'utf-8'
            content = email_message.get_payload(decode=True)
            
            if content:
                content_str = content.decode(charset, errors='ignore')
                if content_type == 'text/html':
                    content_html = content_str
                else:
                    content_text = content_str
        
        return content_text, content_html, attachments
    
    def mark_as_read(self, email_uid: str) -> bool:
        """将邮件标记为已读"""
        if not self.imap_conn:
            return False
        
        try:
            self.imap_conn.store(email_uid, '+FLAGS', '\\Seen')
            return True
        except Exception as e:
            self.logger.error(f"标记邮件已读失败: {e}")
            return False
    
    def should_forward_email(self, email_info: EmailInfo, rules: ForwardRules) -> bool:
        """判断是否应该转发邮件"""
        # 如果配置为转发所有邮件
        if rules.forward_all:
            # 检查黑名单
            if rules.sender_blacklist:
                for blocked_sender in rules.sender_blacklist:
                    if blocked_sender.lower() in email_info.sender.lower():
                        self.logger.info(f"邮件发送者在黑名单中，跳过转发: {email_info.sender}")
                        return False
            return True
        
        # 检查白名单
        if rules.sender_whitelist:
            for allowed_sender in rules.sender_whitelist:
                if allowed_sender.lower() in email_info.sender.lower():
                    return True
        
        # 检查关键词
        if rules.keywords:
            email_content = (email_info.subject + " " + email_info.content_text).lower()
            for keyword in rules.keywords:
                if keyword.lower() in email_content:
                    return True
        
        return False
