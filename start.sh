#!/bin/bash

# 163邮箱转发服务启动脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Python环境
check_python() {
    print_info "检查Python环境..."
    
    if ! command -v python3 &> /dev/null; then
        print_error "Python3 未安装"
        exit 1
    fi
    
    python_version=$(python3 --version | cut -d' ' -f2)
    print_success "Python版本: $python_version"
}

# 检查虚拟环境
check_venv() {
    print_info "检查虚拟环境..."
    
    if [ ! -d ".venv" ]; then
        print_warning "虚拟环境不存在，正在创建..."
        if command -v uv &> /dev/null; then
            uv venv -p 3.11
        else
            python3 -m venv .venv
        fi
        print_success "虚拟环境创建完成"
    else
        print_success "虚拟环境已存在"
    fi
}

# 激活虚拟环境
activate_venv() {
    print_info "激活虚拟环境..."
    source .venv/bin/activate
    print_success "虚拟环境已激活"
}

# 安装依赖
install_deps() {
    print_info "检查并安装依赖..."
    
    if command -v uv &> /dev/null; then
        uv pip install -r requirements.txt
    else
        pip install -r requirements.txt
    fi
    
    print_success "依赖安装完成"
}

# 检查配置文件
check_config() {
    print_info "检查配置文件..."
    
    if [ ! -f "config.yaml" ]; then
        print_error "配置文件 config.yaml 不存在"
        print_info "请复制 config.yaml.example 并修改配置"
        exit 1
    fi
    
    # 检查是否包含默认配置
    if grep -q "<EMAIL>" config.yaml; then
        print_warning "检测到默认配置，请修改 config.yaml 中的邮箱配置"
        print_info "需要修改的配置项："
        print_info "  - email_163.username: 您的163邮箱地址"
        print_info "  - email_163.password: 您的SMTP授权码"
        print_info "  - forward.target_emails: 转发目标邮箱地址"
        read -p "是否继续？(y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
    
    print_success "配置文件检查完成"
}

# 运行测试
run_test() {
    print_info "运行配置测试..."
    
    if python main.py --test; then
        print_success "配置测试通过"
    else
        print_error "配置测试失败，请检查配置"
        exit 1
    fi
}

# 启动服务
start_service() {
    print_info "启动163邮箱转发服务..."
    
    case "$1" in
        "foreground"|"fg")
            print_info "前台模式运行..."
            python main.py
            ;;
        "background"|"bg")
            print_info "后台模式运行..."
            nohup python main.py > service.log 2>&1 &
            echo $! > service.pid
            print_success "服务已在后台启动，PID: $(cat service.pid)"
            print_info "日志文件: service.log"
            print_info "停止服务: ./stop.sh"
            ;;
        *)
            print_info "前台模式运行..."
            python main.py
            ;;
    esac
}

# 显示帮助信息
show_help() {
    echo "163邮箱转发服务启动脚本"
    echo ""
    echo "用法: $0 [选项] [模式]"
    echo ""
    echo "选项:"
    echo "  --help, -h     显示帮助信息"
    echo "  --test, -t     只运行测试，不启动服务"
    echo "  --skip-test    跳过配置测试"
    echo ""
    echo "模式:"
    echo "  foreground, fg 前台运行（默认）"
    echo "  background, bg 后台运行"
    echo ""
    echo "示例:"
    echo "  $0                    # 前台运行"
    echo "  $0 background         # 后台运行"
    echo "  $0 --test             # 只运行测试"
    echo "  $0 --skip-test fg     # 跳过测试，前台运行"
}

# 主函数
main() {
    local test_only=false
    local skip_test=false
    local mode="foreground"
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --help|-h)
                show_help
                exit 0
                ;;
            --test|-t)
                test_only=true
                shift
                ;;
            --skip-test)
                skip_test=true
                shift
                ;;
            foreground|fg|background|bg)
                mode=$1
                shift
                ;;
            *)
                print_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    print_info "=== 163邮箱转发服务启动脚本 ==="
    
    # 执行检查和初始化
    check_python
    check_venv
    activate_venv
    install_deps
    check_config
    
    # 运行测试
    if [ "$skip_test" = false ]; then
        run_test
    fi
    
    # 如果只是测试模式，则退出
    if [ "$test_only" = true ]; then
        print_success "测试完成"
        exit 0
    fi
    
    # 启动服务
    start_service "$mode"
}

# 运行主函数
main "$@"
