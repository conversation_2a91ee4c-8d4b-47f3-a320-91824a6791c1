#!/usr/bin/env python3
"""
163邮箱转发服务测试脚本
"""

import sys
import unittest
from unittest.mock import Mock, patch, MagicMock
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from config_manager import ConfigManager, EmailConfig, ForwardConfig, ForwardRules
from email_receiver import EmailReceiver, EmailInfo
from email_forwarder import EmailForwarder


class TestConfigManager(unittest.TestCase):
    """配置管理器测试"""
    
    def setUp(self):
        self.config_manager = ConfigManager()
    
    def test_validate_email_format(self):
        """测试邮箱地址格式验证"""
        # 这里可以添加具体的测试用例
        pass


class TestEmailReceiver(unittest.TestCase):
    """邮件接收器测试"""
    
    def setUp(self):
        self.email_config = EmailConfig(
            username="<EMAIL>",
            password="test_password",
            imap_server="imap.163.com",
            imap_port=993,
            smtp_server="smtp.163.com",
            smtp_port=465
        )
        self.receiver = EmailReceiver(self.email_config)
    
    @patch('imaplib.IMAP4_SSL')
    def test_connect_success(self, mock_imap):
        """测试IMAP连接成功"""
        mock_conn = Mock()
        mock_imap.return_value = mock_conn
        mock_conn.login.return_value = None
        
        result = self.receiver.connect()
        self.assertTrue(result)
        mock_conn.login.assert_called_once_with("<EMAIL>", "test_password")
    
    @patch('imaplib.IMAP4_SSL')
    def test_connect_failure(self, mock_imap):
        """测试IMAP连接失败"""
        mock_imap.side_effect = Exception("Connection failed")
        
        result = self.receiver.connect()
        self.assertFalse(result)
    
    def test_should_forward_email_all(self):
        """测试转发所有邮件的规则"""
        rules = ForwardRules(
            forward_all=True,
            keywords=[],
            sender_whitelist=[],
            sender_blacklist=[]
        )
        
        email_info = EmailInfo(
            uid="1",
            subject="Test Subject",
            sender="<EMAIL>",
            recipients=["<EMAIL>"],
            date="2024-01-01",
            content_text="Test content",
            content_html="",
            attachments=[],
            raw_message=b""
        )
        
        result = self.receiver.should_forward_email(email_info, rules)
        self.assertTrue(result)
    
    def test_should_forward_email_blacklist(self):
        """测试黑名单过滤"""
        rules = ForwardRules(
            forward_all=True,
            keywords=[],
            sender_whitelist=[],
            sender_blacklist=["<EMAIL>"]
        )
        
        email_info = EmailInfo(
            uid="1",
            subject="Test Subject",
            sender="<EMAIL>",
            recipients=["<EMAIL>"],
            date="2024-01-01",
            content_text="Test content",
            content_html="",
            attachments=[],
            raw_message=b""
        )
        
        result = self.receiver.should_forward_email(email_info, rules)
        self.assertFalse(result)


class TestEmailForwarder(unittest.TestCase):
    """邮件转发器测试"""
    
    def setUp(self):
        self.email_config = EmailConfig(
            username="<EMAIL>",
            password="test_password",
            imap_server="imap.163.com",
            imap_port=993,
            smtp_server="smtp.163.com",
            smtp_port=465
        )
        self.forwarder = EmailForwarder(self.email_config)
    
    @patch('smtplib.SMTP_SSL')
    def test_connect_success(self, mock_smtp):
        """测试SMTP连接成功"""
        mock_conn = Mock()
        mock_smtp.return_value = mock_conn
        mock_conn.login.return_value = None
        
        result = self.forwarder.connect()
        self.assertTrue(result)
        mock_conn.login.assert_called_once_with("<EMAIL>", "test_password")
    
    @patch('smtplib.SMTP_SSL')
    def test_connect_failure(self, mock_smtp):
        """测试SMTP连接失败"""
        mock_smtp.side_effect = Exception("Connection failed")
        
        result = self.forwarder.connect()
        self.assertFalse(result)
    
    def test_html_escape(self):
        """测试HTML转义功能"""
        test_text = '<script>alert("test")</script>'
        expected = '&lt;script&gt;alert(&quot;test&quot;)&lt;/script&gt;'
        result = self.forwarder._html_escape(test_text)
        self.assertEqual(result, expected)


def run_integration_test():
    """运行集成测试"""
    print("=== 集成测试 ===")
    
    try:
        # 测试配置加载
        print("1. 测试配置加载...")
        from config_manager import config_manager
        config = config_manager.load_config()
        print(f"   ✓ 配置加载成功: {config.email_163.username}")
        
        # 测试日志系统
        print("2. 测试日志系统...")
        from logger_config import setup_global_logging, get_logger
        setup_global_logging(config.logging)
        logger = get_logger("test")
        logger.info("日志系统测试")
        print("   ✓ 日志系统正常")
        
        # 测试邮件服务初始化
        print("3. 测试邮件服务初始化...")
        from email_service import email_service
        if email_service.initialize():
            print("   ✓ 邮件服务初始化成功")
        else:
            print("   ✗ 邮件服务初始化失败")
            return False
        
        print("=== 集成测试完成 ===")
        return True
        
    except Exception as e:
        print(f"   ✗ 集成测试失败: {e}")
        return False


def main():
    """主函数"""
    print("163邮箱转发服务测试")
    print("=" * 40)
    
    # 运行单元测试
    print("运行单元测试...")
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    print("\n" + "=" * 40)
    
    # 运行集成测试
    success = run_integration_test()
    
    if success:
        print("\n所有测试通过！")
        return 0
    else:
        print("\n测试失败！")
        return 1


if __name__ == '__main__':
    sys.exit(main())
